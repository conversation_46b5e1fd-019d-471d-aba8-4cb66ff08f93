/**
 * Benji AI Agent - Core Implementation
 * 
 * The main AI agent that orchestrates all tools and generates responses
 */

import { streamText, generateId } from 'ai';
import { getModel, getModelByPlan, type ModelName } from './ai-providers';
import { xaiSearchTool } from './tools/xai-search';
import { exaSearchTool } from './tools/exa-search';
import { imageGenerationTool } from './tools/openai-image';
import { prisma } from './db-utils';

export interface BenjiConfig {
  model?: ModelName;
  userId?: string;
  userPlan?: string;
  maxTokens?: number;
  temperature?: number;
  enableTools?: boolean;
  maxSteps?: number;
  personalityPrompt?: string;
  customSystemPrompt?: string;
  useFirstPerson?: boolean;
}

export interface BenjiContext {
  mentionId?: string;
  mentionContent?: string;
  authorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  // New fields for enhanced context
  monitoredAccountInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  mentionAuthorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export class BenjiAgent {
  private config: BenjiConfig;
  
  constructor(config: BenjiConfig = {}) {
    this.config = {
      model: config.model || 'gemini25Flash',
      userId: config.userId,
      userPlan: config.userPlan || 'reply-guy',
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      enableTools: config.enableTools ?? true,
      maxSteps: config.maxSteps || 5,
      personalityPrompt: config.personalityPrompt,
      customSystemPrompt: config.customSystemPrompt,
      useFirstPerson: config.useFirstPerson ?? true,
    };

    // Auto-select model based on user plan if not specified
    if (!config.model && config.userPlan) {
      this.config.model = getModelByPlan(config.userPlan);
    }
  }

  /**
   * Generate a response for a Twitter mention
   */
  async generateMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('mention', context);
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: `Please analyze this tweet and generate an appropriate response:\n\n"${mentionContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate a response for any tweet (Quick Reply feature)
   */
  async generateQuickReply(
    tweetContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('quick-reply', context);
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: `Generate a thoughtful response to this tweet:\n\n"${tweetContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate an enhanced response for a Twitter mention with tool assistance
   */
  async generateEnhancedMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('enhanced', context);
    
    let userPrompt = `Analyze this tweet and generate an enhanced, well-informed response using available tools when beneficial:\n\n"${mentionContent}"`;
    
    // Add context about who mentioned whom
    if (context.mentionAuthorInfo && context.monitoredAccountInfo) {
      userPrompt += `\n\nContext: @${context.mentionAuthorInfo.handle} (${context.mentionAuthorInfo.name}) mentioned you (@${context.monitoredAccountInfo.handle}). Respond as ${context.monitoredAccountInfo.name}.`;
    }
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: userPrompt,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Calculate bullish score for a tweet
   */
  async calculateBullishScore(content: string): Promise<number> {
    const model = getModel(this.config.model!);
    
    const result = streamText({
      model,
      messages: [
        {
          role: 'system',
          content: `You are a sentiment analysis expert. Analyze the sentiment and positivity of tweets and return a "bullish score" from 1-100 where:
          - 1-20: Very negative, bearish, pessimistic
          - 21-40: Somewhat negative, skeptical
          - 41-60: Neutral, mixed sentiment
          - 61-80: Positive, optimistic
          - 81-100: Very positive, bullish, enthusiastic
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: 'user',
          content: `Analyze this tweet and give it a bullish score: "${content}"`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = '';
    for await (const chunk of result.textStream) {
      text += chunk;
    }
    
    const score = parseInt(text.trim().replace(/\D/g, ''));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Calculate importance score for a tweet
   */
  async calculateImportanceScore(content: string, authorInfo?: {
    followers?: number;
    verified?: boolean;
    handle?: string;
  }): Promise<number> {
    const model = getModel(this.config.model!);
    
    // Build context about the author if available
    let authorContext = '';
    if (authorInfo) {
      const followerText = authorInfo.followers ? ` with ${authorInfo.followers.toLocaleString()} followers` : '';
      const verifiedText = authorInfo.verified ? ' (verified account)' : '';
      authorContext = `\nAuthor: @${authorInfo.handle || 'unknown'}${followerText}${verifiedText}`;
    }
    
    const result = streamText({
      model,
      messages: [
        {
          role: 'system',
          content: `You are a social media engagement expert. Analyze tweets and return an "importance score" from 1-100 where:
          - 1-20: Spam, low-value, irrelevant content
          - 21-40: Personal posts, casual mentions with little engagement potential
          - 41-60: Standard social media content, moderate engagement potential
          - 61-80: Valuable content, questions, industry insights, good engagement opportunity
          - 81-100: High-value content, trending topics, influential discussions, urgent responses needed
          
          Consider factors like:
          - Content depth and value
          - Engagement potential (questions, requests, discussions)
          - Author influence and follower count
          - Relevance to business/professional interests
          - Urgency of response needed
          - Opportunity for meaningful conversation
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: 'user',
          content: `Analyze this tweet and give it an importance score: "${content}"${authorContext}`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = '';
    for await (const chunk of result.textStream) {
      text += chunk;
    }
    
    const score = parseInt(text.trim().replace(/\D/g, ''));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Extract AI-enhanced keywords from tweet content
   */
  async extractEnhancedKeywords(content: string): Promise<string[]> {
    const model = getModel(this.config.model!);
    
    const result = streamText({
      model,
      messages: [
        {
          role: 'system',
          content: `You are a content analysis expert. Extract the most important keywords and topics from tweets for categorization and search.
          
          Focus on:
          - Main topics and subjects
          - Industry terms and technical concepts
          - Product names and brand mentions
          - Actionable items and intents
          - Emotional context keywords
          
          Return 3-7 keywords maximum, separated by commas.
          Exclude common words like: the, and, or, but, this, that, have, will, would.
          Prioritize specific, meaningful terms that capture the tweet's essence.
          
          Example: "AI, machine learning, startup, innovation, feedback"`,
        },
        {
          role: 'user',
          content: `Extract keywords from this tweet: "${content}"`,
        },
      ],
      maxTokens: 50,
      temperature: 0.3,
    });

    // Extract keywords from response
    let text = '';
    for await (const chunk of result.textStream) {
      text += chunk;
    }
    
    // Parse comma-separated keywords and clean them
    const keywords = text
      .split(',')
      .map(keyword => keyword.trim().toLowerCase())
      .filter(keyword => keyword.length > 2 && keyword.length < 30)
      .slice(0, 7); // Limit to 7 keywords max
    
    return keywords.length > 0 ? keywords : ['general']; // Fallback if no keywords extracted
  }

  /**
   * Perform comprehensive AI analysis of a tweet
   */
  async performFullAnalysis(content: string, authorInfo?: {
    name?: string;
    handle?: string;
    followers?: number;
    verified?: boolean;
    avatarUrl?: string;
  }): Promise<{
    bullishScore: number;
    importanceScore: number;
    keywords: string[];
    analysisData: {
      sentiment: string;
      priority: string;
      recommendedAction: string;
      confidence: number;
      processingTime: number;
    };
  }> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 Benji: Starting full analysis for tweet content:', content.substring(0, 100) + '...');
      
      // Run analysis in parallel for better performance
      const [bullishScore, importanceScore, keywords] = await Promise.all([
        this.calculateBullishScore(content),
        this.calculateImportanceScore(content, authorInfo),
        this.extractEnhancedKeywords(content),
      ]);
      
      // Determine sentiment category based on bullish score
      let sentiment: string;
      if (bullishScore >= 81) sentiment = 'very_positive';
      else if (bullishScore >= 61) sentiment = 'positive';
      else if (bullishScore >= 41) sentiment = 'neutral';
      else if (bullishScore >= 21) sentiment = 'negative';
      else sentiment = 'very_negative';
      
      // Determine priority based on importance score
      let priority: string;
      if (importanceScore >= 81) priority = 'urgent';
      else if (importanceScore >= 61) priority = 'high';
      else if (importanceScore >= 41) priority = 'medium';
      else if (importanceScore >= 21) priority = 'low';
      else priority = 'ignore';
      
      // Determine recommended action
      let recommendedAction: string;
      if (importanceScore >= 81) {
        recommendedAction = 'respond_immediately';
      } else if (importanceScore >= 61 && bullishScore >= 61) {
        recommendedAction = 'engage_positively';
      } else if (importanceScore >= 61 && bullishScore <= 40) {
        recommendedAction = 'address_concerns';
      } else if (importanceScore >= 41) {
        recommendedAction = 'monitor_and_consider';
      } else {
        recommendedAction = 'archive_or_ignore';
      }
      
      // Calculate confidence based on content length and author info availability
      let confidence = 0.7; // Base confidence
      if (content.length > 50) confidence += 0.1; // Longer content = more context
      if (content.length > 100) confidence += 0.1;
      if (authorInfo?.followers && authorInfo.followers > 1000) confidence += 0.05;
      if (authorInfo?.verified) confidence += 0.05;
      confidence = Math.min(0.95, confidence); // Cap at 95%
      
      const processingTime = Date.now() - startTime;
      
      console.log('✅ Benji: Full analysis completed:', {
        bullishScore,
        importanceScore,
        keywordCount: keywords.length,
        sentiment,
        priority,
        processingTime
      });
      
      return {
        bullishScore,
        importanceScore,
        keywords,
        analysisData: {
          sentiment,
          priority,
          recommendedAction,
          confidence,
          processingTime,
        },
      };
      
    } catch (error) {
      console.error('❌ Benji: Full analysis failed:', error);
      
      // Return fallback analysis if AI fails
      const processingTime = Date.now() - startTime;
      return {
        bullishScore: 50,
        importanceScore: 50,
        keywords: ['general'],
        analysisData: {
          sentiment: 'neutral',
          priority: 'medium',
          recommendedAction: 'monitor_and_consider',
          confidence: 0.3, // Low confidence for fallback
          processingTime,
        },
      };
    }
  }

  /**
   * Core streaming response method
   */
  private async streamResponse(
    messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
    _context: BenjiContext = {}
  ) {
    console.log('🔄 Benji: Starting streamResponse with config:', {
      model: this.config.model,
      enableTools: this.config.enableTools,
      maxTokens: this.config.maxTokens,
      userId: this.config.userId
    });

    try {
      const model = getModel(this.config.model!);
      console.log('✅ Benji: Model loaded successfully');

      const tools = this.config.enableTools ? {
        searchWeb: xaiSearchTool,
        searchKnowledge: exaSearchTool,
        generateImage: imageGenerationTool,
      } : undefined;

      console.log('🛠️ Benji: Tools configured:', this.config.enableTools ? 'Enabled' : 'Disabled');

      const result = streamText({
        model,
        messages,
        tools,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        maxSteps: this.config.maxSteps,
      });

      console.log('🚀 Benji: streamText call initiated');
      return result;
    } catch (error) {
      console.error('❌ Benji Agent streamResponse error:', error);
      console.error('🔍 Benji: Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        config: this.config
      });
      throw new Error(`AI generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build system prompt based on context
   */
  private buildSystemPrompt(type: 'mention' | 'quick-reply' | 'enhanced', context: BenjiContext): string {
    const isFirstPerson = this.config.useFirstPerson ?? true;
    
    let prompt = `You are Benji, an AI assistant specialized in social media engagement.

CRITICAL RESPONSE RULES:
- RESPOND ONLY with the direct tweet reply - NO explanations or context
- BE ULTRA-CONCISE - every word must add value
- NEVER include phrases like "This tweet seems to be..." or "Here's a response:"
- ONLY provide content that is directly relevant to the original tweet
- Stay within 280 characters maximum`;

    if (isFirstPerson) {
      prompt += `
- ALWAYS respond in FIRST PERSON as the account owner (use "I", "my", "me")
- NEVER refer to the account owner in third person or by name
- NEVER say things like "They consistently push boundaries" - you ARE the account owner
- Answer as if you are the account owner responding directly to the conversation`;
    } else {
      prompt += `
- Respond as an EXTERNAL USER who follows this account (use "you", "your", "they")
- Refer to the account owner appropriately (by name or handle when relevant)
- Respond as if you're a knowledgeable follower engaging with their content
- Show respect and appreciation for the account owner's work when appropriate`;
    }

    prompt += `

Your personality:
- Professional yet personable
- Knowledgeable and helpful
- Always concise and direct
- Respectful and positive

Guidelines:
- Match the tone of the original tweet
- Provide immediate value (insights, questions, resources)
- Be conversational and authentic
- Avoid promotional language
- Use tools only when they add specific value to the response`;

    // Add personality prompt if available
    if (this.config.personalityPrompt) {
      prompt += `\n\nPersonality Style: ${this.config.personalityPrompt}`;
    }

    // Add custom system prompt if available
    if (this.config.customSystemPrompt) {
      prompt += `\n\nAdditional Instructions: ${this.config.customSystemPrompt}`;
    }

    // Add final instructions based on response mode
    if (isFirstPerson) {
      prompt += `\n\nRemember: You ARE the account owner. Respond in first person (I/my/me) and always answer in short. Never reference yourself in third person.`;
    } else {
      prompt += `\n\nRemember: You are an external user responding TO the account owner. Use second person (you/your) or third person (they/their) and always answer in short. Be respectful and engaging.`;
    }

    if (type === 'mention') {
      return prompt + `\n\nRespond to this mention with a direct, engaging reply that represents the monitored account professionally.`;
    }
    
    if (type === 'enhanced') {
      let enhancedPrompt = `\n\nThis is an ENHANCED reply request. Use your tools strategically:
- searchWeb (XAI Live Search): For current events, trending topics, real-time information
- searchKnowledge (Exa Search): For deeper context, company info, technical details
- Only use tools when they genuinely improve the response quality
- Generate a thoughtful, well-informed reply that demonstrates enhanced intelligence`;

      // Add identity context if available
      if (context.monitoredAccountInfo) {
        if (isFirstPerson) {
          enhancedPrompt += `\n\nCRITICAL IDENTITY CONTEXT:
- YOU ARE ${context.monitoredAccountInfo.name} (@${context.monitoredAccountInfo.handle})
- This is YOUR Twitter account that is being mentioned
- Respond AS YOURSELF, not as an AI assistant
- Use "I", "my", "me" - you ARE ${context.monitoredAccountInfo.name}
- NEVER address yourself in third person
- NEVER say "${context.monitoredAccountInfo.name} thinks..." - YOU are ${context.monitoredAccountInfo.name}`;
        } else {
          enhancedPrompt += `\n\nCRITICAL IDENTITY CONTEXT:
- You are responding TO ${context.monitoredAccountInfo.name} (@${context.monitoredAccountInfo.handle})
- This is THEIR Twitter account that posted the content
- Respond AS AN EXTERNAL USER, not as the account owner
- Use "you", "your" when addressing them directly
- Use "they", "their" when referring to them in third person
- Show respect and appreciation for their work when appropriate`;
        }
      }
      
      if (isFirstPerson) {
        enhancedPrompt += `\n\nREMEMBER: You are responding AS the account owner, not TO them. Use first person always.`;
      } else {
        enhancedPrompt += `\n\nREMEMBER: You are responding TO the account owner as an external user. Use second/third person appropriately.`;
      }
      
      return prompt + enhancedPrompt;
    }
    
    return prompt + `\n\nGenerate a direct, authentic reply that adds meaningful value to the conversation.`;
  }

}

// Convenience function for quick usage
export function createBenjiAgent(config: BenjiConfig = {}) {
  return new BenjiAgent(config);
}

// Helper function to get agent for user
export async function getBenjiForUser(userId: string) {
  console.log('🤖 Benji: Getting agent for user:', userId);

  // Validate environment variables
  const requiredEnvVars = ['OPENROUTER_API_KEY'];
  const missingVars = requiredEnvVars.filter(key => !process.env[key]);
  if (missingVars.length > 0) {
    console.error('❌ Benji: Missing environment variables:', missingVars);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Get user's plan, personality, and selected model from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: true,
      selectedPersonality: true,
      selectedModel: true,
    },
  });

  if (!user) {
    console.error('❌ Benji: User not found:', userId);
    throw new Error('User not found');
  }

  console.log('✅ Benji: User found with plan:', user.plan.name);
  console.log('🎭 Benji: Personality:', user.selectedPersonality?.name || 'None');
  console.log('🧠 Benji: Selected model:', user.selectedModel?.name || 'Plan default');
  console.log('📝 Benji: Custom prompt:', user.customSystemPrompt ? 'Yes' : 'No');
  console.log('👤 Benji: First person mode:', user.useFirstPerson ?? true);
  console.log('🔑 Benji: Environment check passed');

  // Determine model to use: user's selected model or plan default
  let modelToUse: ModelName | undefined;
  if (user.selectedModel) {
    // Map database model names to ModelName enum
    const modelNameMap: Record<string, ModelName> = {
      'Workhorse': 'gemini25Flash',
      'Smarty': 'gemini25Pro', 
      'Big Brain': 'openaiO3'
    };
    modelToUse = modelNameMap[user.selectedModel.name];
  }

  return new BenjiAgent({
    userId,
    userPlan: user.plan.name,
    model: modelToUse, // Use selected model or fallback to plan default
    enableTools: true,
    personalityPrompt: user.selectedPersonality?.systemPrompt,
    customSystemPrompt: user.customSystemPrompt || undefined,
    useFirstPerson: user.useFirstPerson ?? true,
  });
}